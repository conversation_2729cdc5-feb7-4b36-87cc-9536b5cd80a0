# MSPA Stream Deck Plugin

A Stream Deck plugin for controlling MSPA (M-SPA) hot tubs and spas. This plugin allows you to monitor water temperature and control heater, filter, and UVC systems directly from your Stream Deck.

## Features

- **Real-time Temperature Display**: Shows current water temperature and target temperature
- **Heater Control**: Short press to toggle heater on/off
- **Filter System Control**: Long press (>500ms) to toggle entire filter system (filter + UVC)
- **Smart Sequencing**: Automatically handles device dependencies (e.g., heater requires filter to be on)
- **Visual Feedback**: Color-coded icons showing system status
- **Auto-refresh**: Configurable refresh intervals for status updates

## Prerequisites

- [Node.js](https://nodejs.org/) (v16 or higher)
- [pnpm](https://pnpm.io/) package manager
- [Stream Deck software](https://www.elgato.com/en/downloads)
- [Elgato Stream Deck CLI](https://docs.elgato.com/sdk/plugins/getting-started)
- MSPA account with spa/hot tub registered

## Installation & Setup

### 1. Install Dependencies

```bash
# Install pnpm if you haven't already
npm install -g pnpm

# Install project dependencies
pnpm install
```

### 2. Install Elgato CLI

```bash
# Install the Elgato Stream Deck CLI globally
npm install -g @elgato/cli
```

### 3. Development Commands

```bash
# Build the plugin
pnpm run build

# Watch for changes and auto-rebuild
pnpm run watch

# Package the plugin for distribution
pnpm run pack
```

### 4. Plugin Development Workflow

```bash
# Start development with auto-reload
pnpm run watch

# In another terminal, link the plugin to Stream Deck
streamdeck link com.trystan34.mspa.sdPlugin

# Or restart Stream Deck to reload changes
streamdeck restart
```

## MSPA API Overview

The MSPA API is a REST-based service that controls M-SPA hot tubs and spas. This plugin interfaces with the official MSPA IoT API.

### API Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Stream Deck   │───▶│    Plugin    │───▶│   MSPA API      │
│     Button      │    │   (Node.js)  │    │ (api.iot.the-   │
└─────────────────┘    └──────────────┘    │  mspa.com)      │
                                           └─────────────────┘
```

### Authentication Flow

1. **User Credentials**: Email and password (hashed with MD5)
2. **Token Request**: Plugin requests authentication token
3. **Token Storage**: Token saved for subsequent requests
4. **Auto-renewal**: Automatic re-authentication when token expires

### API Endpoints

#### Authentication

```
POST /api/enduser/get_token/
```

- Exchanges credentials for access token
- Token valid for extended period
- Required headers: `appid`, `nonce`, `ts`, `sign`

#### Device Status

```
GET /api/device/thing_shadow/
```

- Retrieves current device state
- Returns temperature, heater status, filter status, etc.
- Requires valid authentication token

#### Device Control

```
POST /api/device/command
```

- Sends control commands to device
- Commands: heater_state, filter_state, uvc_state, etc.
- Values: 0 (off) or 1 (on)

### Device State Properties

| Property              | Type    | Description                    |
| --------------------- | ------- | ------------------------------ |
| `water_temperature`   | number  | Current water temperature (°C) |
| `temperature_setting` | number  | Target temperature (°C)        |
| `heater_state`        | 0/1     | Heater on/off status           |
| `filter_state`        | 0/1     | Filter pump on/off status      |
| `uvc_state`           | 0/1     | UVC sterilizer on/off status   |
| `bubble_state`        | 0/1     | Bubble system on/off status    |
| `is_online`           | boolean | Device connectivity status     |

## Configuration

### Required Settings

1. **Email**: Your MSPA account email
2. **Password**: Your MSPA account password (automatically hashed)
3. **Device ID**: Your spa's device identifier
4. **Product ID**: Your spa's product identifier

### Optional Settings

- **Temperature Unit**: Celsius or Fahrenheit display
- **Refresh Interval**: How often to update status (default: 30 seconds)

### Finding Device Information

1. Log into the MSPA mobile app
2. Navigate to device settings
3. Find Device ID and Product ID in device information
4. Or use the MSPA web portal at [mspa.com](https://mspa.com)

## Plugin Behavior

### Short Press (Heater Control)

- **Heater ON → OFF**: Turns off heater immediately
- **Heater OFF → ON**:
    - If filter is ON: Turns on heater
    - If filter is OFF: Turns on filter first, then heater

### Long Press (Filter System Control)

- **System ON → OFF**: Turns off heater → UVC → filter (in sequence)
- **System OFF → ON**: Turns on filter → UVC → heater (in sequence)

### Visual Indicators

- **Green Heater Icon**: Heater is active
- **Red Heater Icon**: Heater is inactive
- **Temperature Display**: Current/Target temperature
- **Loading Animation**: During operations
- **Error Icon**: Connection or configuration issues

## Development

### Project Structure

```
src/
├── actions/
│   └── mspa-control.ts     # Main plugin logic
├── plugin.ts               # Plugin entry point
└── types/
    └── mspa-settings.ts    # TypeScript interfaces

com.trystan34.mspa.sdPlugin/
├── manifest.json           # Plugin metadata
├── ui/
│   └── mspa-control.html   # Property inspector UI
└── imgs/                   # Plugin icons
```

### Key Files

- **`src/actions/mspa-control.ts`**: Core plugin functionality
- **`src/plugin.ts`**: Plugin initialization and logging setup
- **`manifest.json`**: Plugin configuration and metadata
- **`ui/mspa-control.html`**: Settings interface

### Building & Testing

```bash
# Development build with watch
pnpm run watch

# Production build
pnpm run build

# Create distribution package
pnpm run pack

# Link plugin for testing
streamdeck link com.trystan34.mspa.sdPlugin

# View plugin logs
streamdeck logs
```

### Debugging

1. **Enable Debug Logging**: Change log level in `src/plugin.ts`
2. **View Logs**: Use `streamdeck logs` command
3. **Test API**: Use browser dev tools or Postman
4. **Property Inspector**: Right-click button → "Property Inspector"

## API Security

### Authentication Signature

The MSPA API uses a signature-based authentication system:

```typescript
const sign = md5(`${appId}${nonce}${ts}${appSecret}`);
```

- **appId**: Application identifier
- **nonce**: Random 32-character string
- **ts**: Current timestamp
- **appSecret**: Application secret key

### Request Headers

```javascript
{
  'push_type': 'Android',
  'authorization': 'token',
  'appid': appId,
  'nonce': nonce,
  'ts': ts,
  'lan_code': 'EN',
  'sign': sign,
  'content-type': 'application/json; charset=UTF-8'
}
```

## Troubleshooting

### Common Issues

1. **"Setup Required" Icon**
    - Check all credentials are entered correctly
    - Verify device ID and product ID

2. **Connection Errors**
    - Check internet connectivity
    - Verify MSPA account is active
    - Try re-entering password

3. **Commands Not Working**
    - Ensure spa is online and connected to WiFi
    - Check if manual control works in MSPA app
    - Verify device permissions

### Log Analysis

```bash
# View real-time logs
streamdeck logs --follow

# Filter for MSPA plugin logs
streamdeck logs | grep MSPA
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Advanced Configuration

### Environment Variables

You can set environment variables for development:

```bash
# Set custom API endpoint (for testing)
export MSPA_API_BASE=https://api.iot.the-mspa.com/api

# Enable verbose logging
export MSPA_DEBUG=true
```

### Custom Build Configuration

The plugin uses Rollup for building. Configuration in `rollup.config.mjs`:

```javascript
// Custom build settings
export default {
    input: "src/plugin.ts",
    output: {
        file: "com.trystan34.mspa.sdPlugin/bin/plugin.js",
        format: "cjs",
        sourcemap: true,
    },
    // ... other settings
};
```

### Plugin Manifest

Key settings in `manifest.json`:

```json
{
    "Name": "MSPA Control",
    "Version": "1.0.0",
    "Author": "Your Name",
    "Actions": [
        {
            "UUID": "com.trystan34.mspa.control",
            "Icon": "imgs/actions/counter/icon",
            "Name": "MSPA Control",
            "PropertyInspectorPath": "ui/mspa-control.html",
            "Tooltip": "Control your MSPA hot tub",
            "SupportedInMultiActions": false,
            "States": [
                {
                    "Image": "imgs/actions/counter/key"
                }
            ]
        }
    ]
}
```

## API Rate Limiting

The MSPA API has rate limiting in place:

- **Authentication**: 10 requests per minute
- **Status Requests**: 60 requests per minute
- **Commands**: 30 requests per minute

The plugin automatically handles rate limiting with:

- Intelligent polling intervals
- Token caching and reuse
- Exponential backoff on failures

## Error Codes

Common MSPA API error codes:

| Code  | Message                 | Solution                  |
| ----- | ----------------------- | ------------------------- |
| 10001 | Token is not authorized | Re-authenticate           |
| 10002 | Device not found        | Check device ID           |
| 10003 | Device offline          | Check spa WiFi connection |
| 10004 | Invalid command         | Check command format      |
| 10005 | Rate limit exceeded     | Reduce request frequency  |

## Performance Optimization

### Memory Usage

- Token caching reduces authentication requests
- Efficient polling with change detection
- Automatic cleanup of intervals and timeouts

### Network Efficiency

- Conditional status updates only when data changes
- Compressed request/response handling
- Connection pooling for multiple requests

### UI Responsiveness

- Asynchronous operations prevent blocking
- Loading states provide user feedback
- Error recovery with automatic retry

## Testing

### Unit Testing

```bash
# Run tests (if implemented)
pnpm test

# Run tests with coverage
pnpm test:coverage
```

### Integration Testing

```bash
# Test with real MSPA device
pnpm test:integration

# Test API endpoints
pnpm test:api
```

### Manual Testing Checklist

- [ ] Plugin loads without errors
- [ ] Configuration UI works properly
- [ ] Authentication succeeds with valid credentials
- [ ] Status updates display correctly
- [ ] Short press toggles heater
- [ ] Long press toggles filter system
- [ ] Error states display appropriately
- [ ] Temperature units convert correctly
- [ ] Refresh intervals work as configured

## Deployment

### Creating a Release

```bash
# Build production version
pnpm run build

# Create distribution package
pnpm run pack

# The .streamDeckPlugin file will be created
```

### Distribution

1. **Direct Installation**: Double-click the `.streamDeckPlugin` file
2. **Stream Deck Store**: Submit through Elgato's developer portal
3. **GitHub Releases**: Attach to release for download

### Version Management

Update version in multiple places:

- `package.json`
- `manifest.json`
- `README.md`

```bash
# Use npm version to update all at once
npm version patch  # or minor, major
```

## Disclaimer

This plugin is not officially affiliated with MSPA. Use at your own risk. Always ensure your spa is properly maintained and monitored.

## Support

For issues and support:

1. Check the troubleshooting section above
2. Review plugin logs using `streamdeck logs`
3. Create an issue on GitHub with logs and system information
4. Include MSPA device model and firmware version
