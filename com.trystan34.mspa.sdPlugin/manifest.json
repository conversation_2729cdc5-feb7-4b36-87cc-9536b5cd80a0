{"Name": "mspa", "Version": "*******", "Author": "Trystan34", "Actions": [{"Name": "MSPA Control", "UUID": "com.trystan34.mspa.control", "Icon": "imgs/actions/counter/icon", "Tooltip": "Control your MSPA hot tub heating - displays temperature and heater status.", "PropertyInspectorPath": "ui/mspa-control.html", "Controllers": ["Keypad"], "States": [{"Image": "imgs/actions/counter/key", "TitleAlignment": "middle"}]}], "Category": "mspa", "CategoryIcon": "imgs/plugin/category-icon", "CodePath": "bin/plugin.js", "Description": "Control your MSPA hot tub directly from Stream Deck", "Icon": "imgs/plugin/marketplace", "SDKVersion": 2, "Software": {"MinimumVersion": "6.5"}, "OS": [{"Platform": "mac", "MinimumVersion": "12"}, {"Platform": "windows", "MinimumVersion": "10"}], "Nodejs": {"Version": "20", "Debug": "enabled"}, "UUID": "com.trystan34.mspa"}